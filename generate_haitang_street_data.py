import pandas as pd
import random
from datetime import datetime, timedelta
import calendar

# 预设参数
START_DATE = "2023-12-21"
END_DATE = "2025-12-21"
OUTPUT_FILENAME = "海棠区电信诈骗街道警情数据.csv"

# 海棠区的派出所列表
POLICE_STATIONS = [
    "朝阳路所", "星辉所", "东湖所", "新港所", "红旗所", "翠竹所",
    "阳光所", "松柏所", "金山所", "平合所", "青松所"
]

# 模拟街道与派出所的映射关系
# 学习规则:
# - 一个街道 "清溪路" 包含4个派出所
# - 一个街道 "祥和街" 包含2个派出所
# - 其他街道与派出所为一对一关系
STREET_TO_STATION_MAP = {
    "清溪路": ["平合所", "青松所", "金山所", "阳光所"],
    "祥和街": ["星辉所", "东湖所"],
    "朝阳路": ["朝阳路所"],
    "新港大道": ["新港所"],
    "红旗广场": ["红旗所"],
    "翠竹巷": ["翠竹所"],
    "松柏里": ["松柏所"]
}

# 为了方便查找，创建一个从派出所到街道的反向映射
STATION_TO_STREET_MAP = {}
for street, stations in STREET_TO_STATION_MAP.items():
    for station in stations:
        STATION_TO_STREET_MAP[station] = street

def generate_monthly_incidents():
    """根据概率分布生成一个月的总警情数"""
    if random.random() < 0.8:
        return random.randint(200, 350)
    else:
        # 允许在更宽的范围内波动
        return random.randint(50, 450)

def generate_data_for_date_range(start_date_str, end_date_str):
    """在指定的日期范围内生成警情数据"""
    all_data = []
    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

    current_date = start_date
    while current_date <= end_date:
        # 按月处理
        year, month = current_date.year, current_date.month
        _, num_days_in_month = calendar.monthrange(year, month)
        
        month_start_date = datetime(year, month, 1)
        month_end_date = datetime(year, month, num_days_in_month)

        # 1. 生成当月总警情数
        total_monthly_incidents = generate_monthly_incidents()
        
        # 2. 将警情数随机分配到当月的每一天和每个派出所
        # 创建一个字典来存储每天每个派出所的警情数
        # 格式: {(date, station): count}
        incidents_distribution = {}
        for day in range(1, num_days_in_month + 1):
            for station in POLICE_STATIONS:
                d = datetime(year, month, day).date()
                incidents_distribution[(d, station)] = 0

        # 随机分配
        for _ in range(total_monthly_incidents):
            random_day = random.randint(1, num_days_in_month)
            random_date = datetime(year, month, random_day).date()
            random_station = random.choice(POLICE_STATIONS)
            incidents_distribution[(random_date, random_station)] += 1
            
        # 3. 格式化数据并添加到总列表
        for (report_date, station), count in incidents_distribution.items():
            if count > 0:
                street = STATION_TO_STREET_MAP.get(station, "未知街道")
                
                record = {
                    "派出所名": station,
                    "街道名": street,
                    "所属日期": report_date.strftime("%Y-%m-%d"),
                    "开始时间": f"{report_date.strftime('%Y-%m-%d')} 00:00:00",
                    "结束时间": f"{report_date.strftime('%Y-%m-%d')} 23:59:59",
                    "数量": count
                }
                all_data.append(record)

        # 前进到下个月的第一天
        current_date = month_end_date + timedelta(days=1)
        
    return all_data

def main():
    """主函数，生成数据并保存到CSV文件"""
    print("开始生成海棠区街道警情模拟数据...")
    
    # 确保所有派出所都已映射
    mapped_stations = set(STATION_TO_STREET_MAP.keys())
    if set(POLICE_STATIONS) != mapped_stations:
        unmapped = set(POLICE_STATIONS) - mapped_stations
        print(f"警告: 以下派出所未分配到任何街道: {unmapped}")
        return

    data = generate_data_for_date_range(START_DATE, END_DATE)
    
    if not data:
        print("没有生成任何数据，请检查��辑。")
        return

    df = pd.DataFrame(data)
    
    # 确保字段顺序
    df = df[["派出所名", "街道名", "所属日期", "开始时间", "结束时间", "数量"]]
    
    # 保存到CSV
    df.to_csv(OUTPUT_FILENAME, index=False, encoding='utf-8-sig')
    
    print(f"数据生成完毕，已保存到文件: {OUTPUT_FILENAME}")
    print(f"总共生成了 {len(df)} 条记录，总警情数为 {df['数量'].sum()}。")

if __name__ == "__main__":
    main()