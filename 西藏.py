from hybaseapi.TRSConnection import TRSConnection
from hybaseapi.ConnectParams import ConnectParams
from hybaseapi.SearchParams import SearchParams
from datetime import datetime, timedelta
from pyspark.sql import SparkSession, DataFrame

# 配置 Hybase 数据库连接
host = "***********"
port = "5555"
username = "admin"
password = "trs@300229"
tables_and_conditions = {
    "microblog.tc_msg_microblog": "weibo",  # 微博
    "wechat.tc_msg_wechat_public_group_20201225": "wechat",  # 微信
    "other.tc_msg_news": "news",  # 境内新闻网站
    "other.tc_msg_news_app_trs": "app",  # app
    "other.tc_msg_news_comment": "news_comment",  # 新闻评论
    "other.tc_msg_blog": "blog",  # 博客
    "other.tc_msg_selfmedia": "self_media",  # 自媒体
    "other.tc_msg_abroad_news_article": "abroad_news",  # 境外新闻
    "other.tc_msg_abroad_forum": "abroad_forum",  # 境外论坛贴吧
}
mutiple_table_platform_list = [
    {
        "label": "论坛贴吧",
        "platform": "forum_tieba",
        "tableNames": ["other.tc_msg_tieba", "other.tc_msg_forum"],
    },
]
table_inner_platform_list = [
    {
        "label": "脸书",
        "platform": "facebook",
        "tableName": "other.tc_msg_social",
        "conditions": ["trs_m_site_name:Facebook"],
    },
    {
        "label": "推特",
        "platform": "twitter",
        "tableName": "other.tc_msg_social",
        "conditions": ["trs_m_site_name:Twitter"],
    },
]

publish_time_simple_table_platform_map = {
    "other.tc_msg_selfmedia_comment": "self_media_comment",  # 自媒体评论
    "other.tc_msg_short_video_comment": "short_video_comment",  # 短视频评论
}

publish_time_mutiple_table_platform_list = [
    {
        "label": "其他",
        "platform": "other",
        "tableNames": [
            "wechat.tc_msg_wechat_pyq",
            "other.tc_msg_app_sw",
            "other.tc_msg_qq_dt",
        ],
    },
]

# 返回空的 DataFrame
spark = SparkSession.builder.appName("Hybase Data Processing").getOrCreate()


def create_hybase_connection(host: str, port: int, username: str, password: str):
    url = f"http://{host.split(',')[0]}:{port}"
    return TRSConnection(url, username, password, ConnectParams())


def write_to_ck(
    df=DataFrame,
    host=str,
    port=int,
    database=str,
    table_name=str,
    username=str,
    password=str,
):
    url = f"jdbc:clickhouse://{host}:{port}/{database}"
    prop = {
        "user": username,
        "password": password,
        "driver": "com.clickhouse.jdbc.ClickHouseDriver",
        "createTableOptions": "ENGINE = MergeTree() ORDER BY hour",
    }
    df.write.mode("append").jdbc(url, table_name, properties=prop)


def split_time_by_hour(begin_time: datetime, end_time: datetime):
    time_range = []
    current_time = begin_time
    while current_time < end_time:
        time_range.append(current_time.strftime("%Y%m%d%H"))
        current_time += timedelta(hours=1)
    return time_range


def get_query_params():
    start_date = "#{system.last_success_execution_datetime}"
    begin_time = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
    end_time = datetime.now()

    # 将结束时间调整为上一个整点
    adjusted_end_time = end_time.replace(minute=0, second=0, microsecond=0)

    # 如果调整后的结束时间小于开始时间，则使用原始结束时间
    if adjusted_end_time <= begin_time:
        adjusted_end_time = end_time

    time_range = split_time_by_hour(begin_time, adjusted_end_time)
    return (
        time_range,
        #f"m_publish_time:[{int(begin_time.timestamp()*1000)} TO {int(adjusted_end_time.timestamp()*1000)}]",
        f"m_insert_hybase_time:[{int(begin_time.timestamp()*1000)} TO {int(adjusted_end_time.timestamp()*1000)}]",
    )



def query_for_category_maps(conn: TRSConnection, where: str):
    category_maps = {}
    for table_name, platform in tables_and_conditions.items():
        # 动态替换微信表字段
        if table_name == "wechat.tc_msg_wechat_public_group_20201225":
            modified_where = where.replace("g_asp:", "trs_g_asp:")
            replace_log = f"（已替换g_asp->trs_g_asp）"
        else:
            modified_where = where
            replace_log = ""

        # 执行查询
        resultSet = conn.categoryQuery(
            table_name, modified_where, None, "m_insert_hybase_time", 1000000
        )

        # 打印带条件的调试信息
        print(
            f"平台：{platform}，表名：{table_name}\n"
            f"原始条件: {where}\n"
            f"实际条件: {modified_where}{replace_log}\n"
            f"查询结果: {resultSet.getNumFound()}条"
            "\n————————————————————"
        )

        category_maps[platform] = resultSet.getCategoryMap()
    return category_maps


def query_mutiple_table_platform_for_category_maps(
    conn: TRSConnection, where: str, time_range: list
):
    category_maps = {}
    for platform in mutiple_table_platform_list:
        category_map_list = []
        platformName = platform["platform"]
        for table_name in platform["tableNames"]:
            resultSet = conn.categoryQuery(
                table_name, where, None, "m_insert_hybase_time", 1000000
            )
            print(
                f"平台：{platformName}，表名：{table_name}，查询数量：{resultSet.getNumFound()}"
            )
            category_map_list.append(resultSet.getCategoryMap())
        category_maps[platformName] = merge_category_map_list(
            time_range, category_map_list
        )
    return category_maps


def merge_category_map_list(time_range: list, category_map_list: list):
    new_category_map = {}
    for time_point in time_range:
        count = 0
        for category_map in category_map_list:
            count += category_map.get(time_point, 0)
        new_category_map[time_point] = count
    return new_category_map


def query_table_inner_platform_for_category_maps(conn: TRSConnection, where: str):
    category_maps = {}
    for platform in table_inner_platform_list:
        platformName = platform["platform"]
        table_name = platform["tableName"]
        platform_conditions = platform["conditions"]
        conditions = where
        for pc in platform_conditions:
            conditions += f" AND {pc}"
        resultSet = conn.categoryQuery(
            table_name, conditions, None, "m_insert_hybase_time", 1000000
        )
        print(
            f"平台：{platformName}，表名：{table_name}，查询数量：{resultSet.getNumFound()}"
        )
        category_maps[platformName] = resultSet.getCategoryMap()
    return category_maps


def query_publish_time_simple_table_platform_for_category_maps(
    conn: TRSConnection, where: str
):
    category_maps = {}
    for table_name, platform in publish_time_simple_table_platform_map.items():
        #group_fields = "trs_m_publish_time"
        group_fields = "m_insert_hybase_time"
        resultSet = conn.categoryQuery(table_name, where, None, group_fields, 1000000)
        categoryMap = convert_category_map(resultSet.getCategoryMap())
        print(
            f"平台：{platform}，表名：{table_name}，查询数量：{resultSet.getNumFound()}"
        )
        # print(f"平台：{platform}，表名：{table_name}，查询数量：{resultSet.getNumFound()}, 查询结果：{categoryMap}")
        category_maps[platform] = categoryMap
    return category_maps


def convert_category_map(category_map: dict):
    new_category_map = {}
    for k, v in category_map.items():
        new_category_map[f"{k[:4]}{k[5:7]}{k[8:10]}00"] = v
    return new_category_map


def query_publish_time_mutiple_table_platform_for_category_maps(
    conn: TRSConnection, where: str, time_range: list
):
    category_maps = {}
    for platform in publish_time_mutiple_table_platform_list:
        category_map_list = []
        platformName = platform["platform"]
        for table_name in platform["tableNames"]:
            resultSet = conn.categoryQuery(
                #table_name, where, None, "trs_m_publish_time", 1000000
                table_name, where, None, "m_insert_hybase_time", 1000000
            )
            print
            categoryMap = convert_category_map(resultSet.getCategoryMap())
            print(
                f"平台：{platformName}，表名：{table_name}，查询数量：{resultSet.getNumFound()}"
            )
            # print(f"平台：{platformName}，表名：{table_name}，查询数量：{resultSet.getNumFound()}, 查询结果：{categoryMap}")
            category_map_list.append(categoryMap)
        category_maps[platformName] = merge_category_map_list(
            time_range, category_map_list
        )
    return category_maps


def create_table_rows(category_maps: dict, time_range: list):
    rows = []
    for time_point in time_range:
        row = {}
        total = 0
        dt = datetime.strptime(time_point, "%Y%m%d%H")
        formatted_time = dt.strftime("%Y年%m月%d日%H时")

        # 从格式化的字符串提取所需的部分
        row["year"] = formatted_time[:5]  # 'xxxx年'
        row["month"] = formatted_time[:8]  # 'xx月'
        row["day"] = formatted_time[:11]  # 'xx日'
        row["hour"] = formatted_time  # 'xxxx年xx月xx日xx小时'
        row["insert_time"] = dt
        for platform, category_map in category_maps.items():
            count = category_map.get(time_point, 0)
            row[f"{platform}_count"] = count
            total += count
        row["total_count"] = total
        rows.append(row)
    return rows


def merge_platform_category_map_result(platform_category_map_list: list):
    new_category_map = {}
    for platform_category_map in platform_category_map_list:
        for k, v in platform_category_map.items():
            new_category_map[k] = v
    return new_category_map


def build_disanfang_rows(conn: TRSConnection, where: str, time_range: list):
    print(
        "==========================================开始第三方数据统计======================================="
    )
    new_where = where + " AND g_asp:trsdc "
    print(f"开始查询数据库，基础查询where：{new_where}")
    category_maps0 = query_for_category_maps(conn, new_where)
    category_maps1 = query_mutiple_table_platform_for_category_maps(
        conn, new_where, time_range
    )
    category_maps2 = query_table_inner_platform_for_category_maps(conn, new_where)
    category_maps3 = query_publish_time_simple_table_platform_for_category_maps(
        conn, new_where
    )
    category_maps4 = query_publish_time_mutiple_table_platform_for_category_maps(
        conn, new_where, time_range
    )
    category_maps = merge_platform_category_map_result(
        [category_maps0, category_maps1, category_maps2, category_maps3, category_maps4]
    )
    print("查询数据库结束:")
    # 封装数据库表记录
    rows = create_table_rows(category_maps, time_range)
    for row in rows:
        row["g_asp"] = "trsdc"
    print(
        "==========================================第三方数据统计结束======================================="
    )
    return rows


def build_yangban_rows(conn: TRSConnection, where: str, time_range: list):
    print(
        "==========================================开始央办数据统计======================================="
    )
    new_where = where + " AND g_asp:yb "
    print(f"开始查询数据库，基础查询where：{new_where}")
    category_maps0 = query_for_category_maps(conn, new_where)
    category_maps1 = query_mutiple_table_platform_for_category_maps(
        conn, new_where, time_range
    )
    category_maps2 = query_table_inner_platform_for_category_maps(conn, new_where)
    category_maps3 = query_publish_time_simple_table_platform_for_category_maps(
        conn, new_where
    )
    category_maps4 = query_publish_time_mutiple_table_platform_for_category_maps(
        conn, new_where, time_range
    )
    category_maps = merge_platform_category_map_result(
        [category_maps0, category_maps1, category_maps2, category_maps3, category_maps4]
    )
    print("查询数据库结束:")
    # 封装数据库表记录
    rows = create_table_rows(category_maps, time_range)
    for row in rows:
        row["g_asp"] = "yb"
    print(
        "==========================================央办数据统计结束======================================="
    )
    return rows

def build_bendicaiji_rows(conn: TRSConnection, where: str, time_range: list):
    print(
        "==========================================开始本地采集数据统计======================================="
    )
    new_where = where + " AND g_asp:trslc "
    print(f"开始查询数据库，基础查询where：{new_where}")
    category_maps0 = query_for_category_maps(conn, new_where)
    category_maps1 = query_mutiple_table_platform_for_category_maps(
        conn, new_where, time_range
    )
    category_maps2 = query_table_inner_platform_for_category_maps(conn, new_where)
    category_maps3 = query_publish_time_simple_table_platform_for_category_maps(
        conn, new_where
    )
    category_maps4 = query_publish_time_mutiple_table_platform_for_category_maps(
        conn, new_where, time_range
    )
    category_maps = merge_platform_category_map_result(
        [category_maps0, category_maps1, category_maps2, category_maps3, category_maps4]
    )
    print("查询数据库结束:")
    # 封装数据库表记录
    rows = create_table_rows(category_maps, time_range)
    for row in rows:
        row["g_asp"] = "trslc"
    print(
        "==========================================本地采集统计结束======================================="
    )
    return rows

def main250424():
    # 拼装查询条件
    time_range, where = get_query_params()
    print(where)
    # 创建海贝数据库连接
    conn = create_hybase_connection(host, port, username, password)
    # 查询数据库
    category_maps0 = query_for_category_maps(conn, where)
    category_maps1 = query_mutiple_table_platform_for_category_maps(
        conn, where, time_range
    )
    category_maps2 = query_table_inner_platform_for_category_maps(conn, where)
    category_maps3 = query_publish_time_simple_table_platform_for_category_maps(
        conn, where
    )
    category_maps4 = query_publish_time_mutiple_table_platform_for_category_maps(
        conn, where, time_range
    )
    category_maps = merge_platform_category_map_result(
        [category_maps0, category_maps1, category_maps2, category_maps3, category_maps4]
    )
    print("分类统计结果:" + str(category_maps))
    # 封装数据库表记录
    rows = create_table_rows(category_maps, time_range)
    print("表记录：")
    for row in rows:
        print(row)
    df = spark.createDataFrame(rows)
    # 写入数据库
    write_to_ck(
        df=df,
        host="************",
        port=8123,
        database="data_display",
        username="admin",
        password="trsadmin@1234",
        table_name="dwd_display_distribution",
    )


def main_test():
    # 拼装查询条件
    time_range, where = get_query_params()
    print(f"查询时间范围：{where}")
    conn = create_hybase_connection(host, port, username, password)
    disanfang_rows = build_disanfang_rows(conn, where, time_range)
    yangban_rows = build_yangban_rows(conn, where, time_range)
    df = spark.createDataFrame(disanfang_rows + yangban_rows)
    print("表记录：")
    df.show(1)


def main():
    # 拼装查询条件
    time_range, where = get_query_params()
    print(f"查询时间范围：{where}")
    conn = create_hybase_connection(host, port, username, password)
    disanfang_rows = build_disanfang_rows(conn, where, time_range)
    yangban_rows = build_yangban_rows(conn, where, time_range)
    bengdicaiji_rows = build_bendicaiji_rows(conn, where, time_range)
    df = spark.createDataFrame(disanfang_rows + yangban_rows + bengdicaiji_rows)
    print("表记录：")
    df.show(1)
    write_to_ck(
        df=df,
        host="************",
        port=8123,
        database="data_display",
        username="admin",
        password="trsadmin@1234",
        table_name="dwd_display_distribution",
    )


if __name__ == "__main__":
    main()
