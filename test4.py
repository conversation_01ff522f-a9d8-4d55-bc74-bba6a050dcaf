import csv
import random
from datetime import datetime, timedelta

# 海棠区派出所列表
STATIONS = ["朝阳路所", "星辉所", "东湖所", "新港所", "红旗所",
            "翠竹所", "阳光所", "松柏所", "金山所", "平合所", "青松所"]

# 案件类型列表（基于图片中的结构）
CASE_TYPES = [
    "侵财.两抢",
    "侵财.盗窃.扒窃",
    "侵财.盗窃.车财",
    "侵财.盗窃.三车",
    "侵财.盗窃.入盗",
    "侵财.盗窃.一般盗窃",
    "侵财.盗窃.总盗窃",  # 计算字段
    "侵财.诈骗.电信诈骗",
    "侵财.诈骗.其他诈骗",
    "侵财.诈骗.总诈骗",  # 计算字段
    "非侵财.殴打他人",
    "非侵财.家暴",
    "非侵财.扰乱秩序",
    "非侵财.损坏公私财物",
    "非侵财.其他"
]

# 日期范围
start_date = datetime(2025, 6, 1)
end_date = datetime(2025, 8, 31)
date_range = [start_date + timedelta(days=x) for x in range(0, (end_date - start_date).days + 1)]

def generate_daily_data(station):
    """为单个派出所生成单日数据"""
    # 生成每日合计值 (30-200)
    total = random.randint(30, 200)

    # 生成各案件类型的数量
    case_counts = {
        "侵财.两抢": random.randint(0, min(2, total//20)),  # 低概率事件
        "侵财.盗窃.扒窃": random.randint(0, total//5),
        "侵财.盗窃.车财": random.randint(0, total//4),
        "侵财.盗窃.三车": random.randint(0, total//6),
        "侵财.盗窃.入盗": random.randint(0, total//5),
        "侵财.盗窃.一般盗窃": random.randint(0, total//3),
        "侵财.诈骗.电信诈骗": random.randint(0, total//4),
        "侵财.诈骗.其他诈骗": random.randint(0, total//6),
        "非侵财.殴打他人": random.randint(0, total//3),
        "非侵财.家暴": random.randint(0, min(5, total//10)),  # 严格控制数量
        "非侵财.扰乱秩序": random.randint(0, total//5),
        "非侵财.损坏公私财物": random.randint(0, total//6),
        "非侵财.其他": random.randint(0, total//4)
    }

    # 计算盗窃总和
    theft_types = ["侵财.盗窃.扒窃", "侵财.盗窃.车财", "侵财.盗窃.三车",
                   "侵财.盗窃.入盗", "侵财.盗窃.一般盗窃"]
    case_counts["侵财.盗窃.总盗窃"] = sum(case_counts[t] for t in theft_types)

    # 计算诈骗总和
    fraud_types = ["侵财.诈骗.电信诈骗", "侵财.诈骗.其他诈骗"]
    case_counts["侵财.诈骗.总诈骗"] = sum(case_counts[t] for t in fraud_types)

    # 调整总数确保逻辑一致
    current_total = sum(v for k, v in case_counts.items() if '总' not in k)
    if current_total != total:
        # 按比例调整非侵财类
        non_property_types = ["非侵财.殴打他人", "非侵财.家暴", "非侵财.扰乱秩序",
                              "非侵财.损坏公私财物", "非侵财.其他"]
        adjustment = total - current_total

        non_property_sum = sum(case_counts[t] for t in non_property_types)

        if non_property_sum > 0:
            for t in non_property_types:
                case_counts[t] = max(0, case_counts[t] + int(adjustment * case_counts[t] / non_property_sum))
        elif non_property_types:
            # 如果非侵财类总和为0，则平均分配调整值
            adjustment_per_type = adjustment // len(non_property_types)
            for t in non_property_types:
                case_counts[t] = max(0, case_counts[t] + adjustment_per_type)


    return total, case_counts

def generate_dataset():
    """生成完整数据集"""
    data = []

    for date in date_range:
        date_str = date.strftime("%Y-%m-%d")
        for station in STATIONS:
            total, case_counts = generate_daily_data(station)

            # 为每个案件类型创建一条记录
            for case_type in CASE_TYPES:
                data.append([
                    station,
                    date_str,
                    case_type,
                    case_counts[case_type],
                    total
                ])

    return data

# 生成并保存数据
if __name__ == "__main__":
    dataset = generate_dataset()

    with open('海棠区每日警情统计.csv', 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.writer(f)
        writer.writerow(["派出所名称", "时间", "类型", "数量", "合计"])
        writer.writerows(dataset)

    print(f"已生成 {len(dataset)} 条记录，保存为：海棠区每日警情统计.csv")
    print(f"时间范围：{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
    print(f"派出所数量：{len(STATIONS)}")
    print(f"案件类型数量：{len(CASE_TYPES)}")