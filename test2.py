import csv
import random

# 海棠区11个派出所
police_stations = [
    "朝阳路所", "星辉所", "东湖所", "新港所", "红旗所",
    "翠竹所", "阳光所", "松柏所", "金山所", "平合所", "青松所"
]

# 创建阈值文件
with open('海棠区派出所日阈值表.csv', 'w', newline='', encoding='utf-8-sig') as f:
    writer = csv.writer(f)
    writer.writerow(["派出所名", "日阈值"])

    # 为每个派出所生成随机阈值(1-25)
    for station in police_stations:
        threshold = random.randint(1, 25)
        writer.writerow([station, threshold])

print("派出所日阈值表已生成 -> 海棠区派出所日阈值表.csv")

# 验证阈值范围 (可选，仅用于确认数据质量)
threshold_values = []
with open('海棠区派出所日阈值表.csv', 'r', encoding='utf-8-sig') as f:
    reader = csv.reader(f)
    next(reader)  # 跳过标题行
    for row in reader:
        threshold_values.append(int(row[1]))

print("\n阈值验证:")
print(f"生成阈值个数: {len(threshold_values)}")
print(f"最小值: {min(threshold_values)}")
print(f"最大值: {max(threshold_values)}")
print(f"全部在1-25范围内: {all(1 <= x <= 25 for x in threshold_values)}")