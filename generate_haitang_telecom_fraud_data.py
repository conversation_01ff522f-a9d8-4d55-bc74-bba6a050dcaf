import csv
import random
from datetime import datetime, timedelta
from collections import defaultdict

# --- 配置参数 ---
OUTPUT_FILENAME = '海棠区每日电信诈骗警情表.csv'
DISTRICT = "海棠区"
START_DATE = datetime(2023, 12, 21)
END_DATE = datetime(2025, 12, 21)

# --- 函数定义 ---

def get_monthly_total():
    """
    生成每月的警情总数。
    80% 的概率在 [200, 350] 范围内，20% 的概率在 [50, 450] 范围内。
    """
    if random.random() < 0.8:
        return random.randint(200, 350)
    else:
        # 为了覆盖50-450的剩余部分
        if random.random() < 0.5:
            return random.randint(50, 199)
        else:
            return random.randint(351, 450)

def distribute_monthly_total_to_days(total, num_days):
    """
    将月度总数随机分配到当月的每一天。
    """
    if total == 0:
        return [0] * num_days

    weights = [random.random() for _ in range(num_days)]
    total_weight = sum(weights)

    daily_counts = [int(total * w / total_weight) for w in weights]

    # 因取整可能产生的误差，将差值加到随机一天
    current_sum = sum(daily_counts)
    diff = total - current_sum
    if diff != 0:
        # 随机选择一天来调整差额
        # 为避免产生负数，只在diff > 0时加给随机一天，或在diff < 0时从数值大于diff绝对值的一天减去
        eligible_indices = [i for i, count in enumerate(daily_counts) if count + diff >= 0]
        if not eligible_indices:
             # 如果没有合格的日期（不太可能发生），则重新分配
             return distribute_monthly_total_to_days(total, num_days)

        random_day_index = random.choice(eligible_indices)
        daily_counts[random_day_index] += diff

    return daily_counts

# --- 主逻辑 ---

# 1. 按月份生成每日警情数量
daily_cases = {}
current_date = START_DATE
while current_date <= END_DATE:
    year = current_date.year
    month = current_date.month

    # 获取当月总天数
    if month == 12:
        days_in_month = 31
    else:
        next_month = datetime(year, month + 1, 1)
        days_in_month = (next_month - datetime(year, month, 1)).days

    # 获取当月的日期列表
    days_of_month = []
    temp_date = datetime(year, month, 1)
    for _ in range(days_in_month):
        if START_DATE <= temp_date <= END_DATE:
            days_of_month.append(temp_date)
        temp_date += timedelta(days=1)

    if days_of_month:
        # 生成月度总数并分配到天
        monthly_total = get_monthly_total()
        distributed_cases = distribute_monthly_total_to_days(monthly_total, len(days_of_month))

        for i, day_date in enumerate(days_of_month):
            daily_cases[day_date] = distributed_cases[i]

    # 前进到下个月
    if month == 12:
        current_date = datetime(year + 1, 1, 1)
    else:
        current_date = datetime(year, month + 1, 1)


# 2. 写入CSV文件
with open(OUTPUT_FILENAME, 'w', newline='', encoding='utf-8-sig') as f:
    writer = csv.writer(f)
    writer.writerow(["区名", "归属日期", "起始时间", "结束时间", "电信诈骗警情数量"])

    # 生成日期范围并写入数据
    date_range = [START_DATE + timedelta(days=x) for x in range((END_DATE - START_DATE).days + 1)]

    total_records = 0
    for date in date_range:
        date_str = date.strftime("%Y-%m-%d")
        start_time = (date - timedelta(days=1)).strftime("%Y-%m-%d 16:00")
        end_time = date.strftime("%Y-%m-%d 16:00")

        cases = daily_cases.get(date, 0)

        writer.writerow([DISTRICT, date_str, start_time, end_time, cases])
        total_records += 1

print(f"'{OUTPUT_FILENAME}' 已生成。")

# --- 数据验证 ---
print(f"\n数据范围: {START_DATE.strftime('%Y-%m-%d')} 至 {END_DATE.strftime('%Y-%m-%d')}")
print(f"总记录数: {total_records}")
print(f"每条记录时间范围: 前一日16:00到当日16:00")
print(f"示例: 2024-01-01的统计数据包含从2023-12-31 16:00至2024-01-01 16:00的警情")


