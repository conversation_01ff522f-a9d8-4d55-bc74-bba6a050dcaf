import csv
import random
from datetime import datetime, timedelta

# 配置参数
DISTRICTS = ["海棠区", "松林区", "玉泉区", "青云区", "金桥区", "龙祥区"]
HAITANG_STATIONS = ["朝阳路所", "星辉所", "东湖所", "新港所", "红旗所",
                    "翠竹所", "阳光所", "松柏所", "金山所", "平合所", "青松所"]
CRIME_TYPES = ["涉黄","涉赌"]

# 生成日期范围 (2025年6月-8月)
start_date = datetime(2025, 6, 1)
end_date = datetime(2025, 8, 31)
date_range = [start_date + timedelta(days=x) for x in range(0, (end_date - start_date).days + 1)]

# 生成CSV文件
with open('星河市涉黄赌警情数据.csv', 'w', newline='', encoding='utf-8-sig') as f:
    writer = csv.writer(f)
    writer.writerow(["区名", "派出所名", "报警时间", "案件类型"])

    # 生成每个区的数据
    for date in date_range:
        date_str = date.strftime("%Y-%m-%d")

        for district in DISTRICTS:
            # 生成当前区当天的案件总量 (30-200)
            daily_cases = random.randint(1, 20)

            # 特殊处理海棠区（需要分配到派出所）
            if district == "海棠区":
                # 为每个派出所随机分配案件数（总和为daily_cases）
                station_cases = []
                remaining = daily_cases

                # 为前10个派出所分配案件
                for i in range(len(HAITANG_STATIONS) - 1):
                    # 确保每个派出所最多不超过每日总量的20%
                    max_per_station = min(remaining, int(daily_cases * 0.2))
                    cases = random.randint(0, max_per_station)
                    station_cases.append(cases)
                    remaining -= cases

                # 剩余案件给最后一个派出所
                station_cases.append(remaining)

                # 生成派出所级别的案件记录
                for station, count in zip(HAITANG_STATIONS, station_cases):
                    for _ in range(count):
                        # 特定派出所的案件类型加权
                        if station == "朝阳路所":
                            crime = random.choices(CRIME_TYPES, weights=[1,1], k=1)[0]
                        elif station == "新港所":
                            crime = random.choices(CRIME_TYPES, weights=[1,1], k=1)[0]
                        else:
                            crime = random.choice(CRIME_TYPES)

                        writer.writerow([district, station, date_str, crime])

            # 非海棠区的处理
            else:
                # 为非海棠区生成案件记录
                for _ in range(daily_cases):
                    # 非海棠区的案件类型加权
                    if district == "松林区":
                        crime = random.choices(CRIME_TYPES, weights=[1,3], k=1)[0]
                    elif district == "玉泉区":
                        crime = random.choices(CRIME_TYPES, weights=[3,1], k=1)[0]
                    else:
                        crime = random.choice(CRIME_TYPES)

                    writer.writerow([district, "", date_str, crime])

print("数据生成完成，已保存到 星河市涉黄赌警情数据.csv")
