import csv
import random
from datetime import datetime, timedelta

# 配置参数
DISTRICTS = ["海棠区", "松林区", "玉泉区", "青云区", "金桥区", "龙祥区"]
START_DATE = datetime(2025, 6, 1)
END_DATE = datetime(2025, 8, 31)

# 生成日期范围
date_range = [START_DATE + timedelta(days=x) for x in range(0, (END_DATE - START_DATE).days + 1)]

# 创建总警情表
with open('每日总警情表.csv', 'w', newline='', encoding='utf-8-sig') as f:
    writer = csv.writer(f)
    writer.writerow(["区名", "归属日期", "起始时间", "结束时间", "总警情数量"])

    # 为每个日期和每个区生成警情数量
    for date in date_range:
        date_str = date.strftime("%Y-%m-%d")

        # 计算时间段（前一天16:00到当前日期16:00）
        start_time = (date - timedelta(days=1)).strftime("%Y-%m-%d 16:00")
        end_time = date.strftime("%Y-%m-%d 16:00")

        for district in DISTRICTS:
            # 生成随机警情数量 (30-200)
            total_cases = random.randint(500, 1000)
            writer.writerow([district, date_str, start_time, end_time, total_cases])

print("每日总警情表已生成 -> 每日总警情表.csv")

# 验证数据范围
print(f"\n数据范围: {START_DATE.strftime('%Y-%m-%d')} 至 {END_DATE.strftime('%Y-%m-%d')}")
print(f"总记录数: {len(DISTRICTS) * len(date_range)}")
print(f"每条记录时间范围: 前一日16:00到当日16:00")
print(f"示例: 2025-06-01的统计数据包含从2025-05-31 16:00至2025-06-01 16:00的警情")