import csv
import random
from datetime import datetime, timedelta

# 海棠区派出所列表
POLICE_STATIONS = ["朝阳路所", "星辉所", "东湖所", "新港所", "红旗所",
                   "翠竹所", "阳光所", "松柏所", "金山所", "平合所", "青松所"]

# 案件类型及其比例权重（基于图片数据分布）
CASE_DISTRIBUTION = {
    '两抢': 0.02,  # 占比2%
    '扒窃': 0.08,
    '车财': 0.10,
    '三车': 0.07,
    '入盗': 0.09,
    '一般盗窃': 0.15,
    '电信诈骗': 0.13,
    '其他诈骗': 0.06,
    '殴打他人': 0.18,
    '家暴': 0.03,
    '扰乱秩序': 0.05,
    '损坏公私财物': 0.04,
    '其他': 0.10
}

# 计算权重总和用于归一化
total_weight = sum(CASE_DISTRIBUTION.values())
# 归一化权重
for key in CASE_DISTRIBUTION:
    CASE_DISTRIBUTION[key] /= total_weight

# 日期范围（2025年6月1日至8月31日）
START_DATE = datetime(2023, 12, 21)
END_DATE = datetime(2024, 5, 31)
DATE_RANGE = [START_DATE + timedelta(days=x) for x in range((END_DATE - START_DATE).days + 1)]

def generate_daily_data():
    """生成单日警情数据"""
    # 每日案件总数（30-200之间）
    total_cases = random.randint(30, 200)

    # 按比例分配各类案件数量
    cases = {}
    for case_type, weight in CASE_DISTRIBUTION.items():
        # 生成基本数量（使用高斯分布更符合实际情况）
        base_count = int(total_cases * weight)
        # 添加随机波动（±30%）
        cases[case_type] = max(0, int(base_count * (1 + random.uniform(-0.3, 0.3))))

    # 确保总和等于总数（调整"其他"类型的数量）
    current_sum = sum(cases.values())
    adjustment = total_cases - current_sum
    cases['其他'] += adjustment

    # 确保没有负值
    cases['其他'] = max(0, cases['其他'])

    # 计算汇总数据
    cases['总盗窃'] = cases['扒窃'] + cases['车财'] + cases['三车'] + cases['入盗'] + cases['一般盗窃']
    cases['总诈骗'] = cases['电信诈骗'] + cases['其他诈骗']

    return total_cases, cases

def generate_full_dataset():
    """生成完整数据集（92天×11个派出所）"""
    # 表头（根据图片中的字段）
    headers = [
        "派出所名称", "日期", "合计",
        "两抢", "总盗窃", "扒窃", "车财", "三车", "入盗", "一般盗窃",
        "总诈骗", "电信诈骗", "其他诈骗",
        "殴打他人", "家暴", "扰乱秩序", "损坏公私财物", "其他"
    ]

    data = []

    # 生成每一天每个派出所的数据
    for date in DATE_RANGE:
        date_str = date.strftime("%Y-%m-%d")
        for station in POLICE_STATIONS:
            total, cases = generate_daily_data()
            row = [
                station, date_str, total,
                cases['两抢'], cases['总盗窃'],
                cases['扒窃'], cases['车财'], cases['三车'], cases['入盗'], cases['一般盗窃'],
                cases['总诈骗'], cases['电信诈骗'], cases['其他诈骗'],
                cases['殴打他人'], cases['家暴'], cases['扰乱秩序'],
                cases['损坏公私财物'], cases['其他']
            ]
            data.append(row)

    return headers, data

def save_to_csv(headers, data, filename):
    """保存为CSV文件"""
    with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.writer(f)
        writer.writerow(headers)
        writer.writerows(data)
    print(f"已生成 {len(data)} 行数据，保存为 {filename}")

# 主程序
if __name__ == "__main__":
    print("开始生成海棠区警情统计数据...")
    print(f"时间范围: {START_DATE.strftime('%Y-%m-%d')} 至 {END_DATE.strftime('%Y-%m-%d')}")
    print(f"派出所数量: {len(POLICE_STATIONS)}")

    # 生成数据
    headers, data = generate_full_dataset()

    # 创建文件名
    start_date_str = START_DATE.strftime('%Y年%m月%d日')
    end_date_str = END_DATE.strftime('%Y年%m月%d日')
    filename = f"海棠区每日警情统计({start_date_str}-{end_date_str}).csv"

    # 保存为CSV
    save_to_csv(headers, data, filename)

    print("数据生成完成！")